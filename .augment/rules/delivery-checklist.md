---
type: "agent_requested"
description: "Defines the final checklist and verification artifacts required to mark a task as complete, including running tests and builds."
---
# Rule: Final Delivery & Verification Checklist

Before a task can be considered complete, you must produce an Automated Validation Report containing the following artifacts:

1.  **Test Run Log:** The complete console output from running the full test suite, showing "ALL TESTS PASSED".
2.  **Build Log:** The complete console output from the production build command, showing zero errors.
3.  **Documentation:** A plain-English README update and any necessary code-level documentation.
4.  **Change Impact Analysis:** A summary of all files changed and any new dependencies added.