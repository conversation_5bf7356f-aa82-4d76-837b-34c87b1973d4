---
type: "agent_requested"
description: "The mandatory, step-by-step protocol for diagnosing and fixing bugs using automated tests and the /debug command."
---
# Rule: Bug Fixing Protocol

To fix a bug, follow this exact procedure:

1.  **Isolate:** Clearly state the bug and how to reproduce it.
2.  **Generate Failing Test:** Use `@rule-vdd-cycle.md` Step 2 to create a single, automated test that fails specifically because of this bug.
3.  **Debug:** Use the `/debug` command. Provide the full error message/stack trace from the failing test.
4.  **Verify:** Apply the suggested fix. Re-run the test. If it passes, run the entire test suite to check for regressions. If it fails, provide the new error message back to the `/debug` command and iterate.