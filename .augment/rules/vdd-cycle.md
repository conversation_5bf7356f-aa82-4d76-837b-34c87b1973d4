---
type: "agent_requested"
description: "The core Verification-Driven Development (VDD) process for writing code: generate failing tests first, then write code to make them pass."
---
# Rule: Verification-Driven Development (VDD) Cycle

This is the mandatory process for all new feature implementation and bug fixing.

1.  **Define the Verification:** Before requesting feature code, first generate a comprehensive test plan that describes the exact behaviors to be tested.
2.  **Generate the Failing Test:** Use the `/test` command to write the automated test code from the plan. This test MUST fail initially. This is the objective "definition of done."
3.  **Generate the Feature Code:** Use `/edit` or `/debug`. Provide the AI with the full context AND the failing test code. Command it to write the minimum code required to make the test pass.
4.  **Refactor on Green:** Only after all tests are passing can you command the AI to refactor the code for quality. Re-run all tests after refactoring to verify no regressions.